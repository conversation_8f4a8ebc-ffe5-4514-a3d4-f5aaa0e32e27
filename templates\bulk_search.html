{% extends "base.html" %}

{% block title %}Bulk Image Search - Image Scraper{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10" id="formSection">
        <div class="card shadow">
            <div class="card-header bg-success text-white">
                <h4 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>Bulk Image Search
                </h4>
            </div>
            <div class="card-body">
                <div class="alert alert-info" role="alert">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Bulk Search:</strong> Add multiple keyword/class combinations to scrape images in batch. 
                    Each row will create a separate folder with the specified class name.
                </div>

                <form id="bulkSearchForm" class="needs-validation" novalidate>
                    <!-- Dynamic Table Section -->
                    <div class="mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="mb-0">
                                <i class="fas fa-table me-2"></i>Search Entries
                            </h5>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="addTableRow()">
                                <i class="fas fa-plus me-1"></i>Add Row
                            </button>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-bordered" id="searchTable">
                                <thead class="table-light">
                                    <tr>
                                        <th style="width: 40%;">
                                            <i class="fas fa-search me-1"></i>Keyword
                                        </th>
                                        <th style="width: 40%;">
                                            <i class="fas fa-folder me-1"></i>Class Name
                                        </th>
                                        <th style="width: 20%;" class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="searchTableBody">
                                    <!-- Initial row will be added by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Configuration Section -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="images_per_class" class="form-label">
                                <i class="fas fa-image me-1"></i>Images per Class *
                            </label>
                            <input type="number" class="form-control" id="images_per_class" name="images_per_class" 
                                   value="100" min="1" max="500" required>
                            <div class="invalid-feedback">Please enter a number between 1 and 500</div>
                            <div class="form-text">Number of images to download for each class</div>
                        </div>
                        <div class="col-md-6">
                            <label for="destination_folder" class="form-label">
                                <i class="fas fa-folder-open me-1"></i>Destination Folder
                            </label>
                            <input type="text" class="form-control" id="destination_folder" name="destination_folder"
                                   placeholder="Leave empty for default folder">
                            <div class="form-text">Optional: Custom folder path (default: scraped_images)</div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-success btn-lg" id="startBulkSearchBtn" onclick="startBulkSearch()">
                            <i class="fas fa-rocket me-2"></i>Start Bulk Search
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- How it Works Card -->
        <div class="card mt-4">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-info-circle me-2"></i>How Bulk Search Works
                </h5>
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Add multiple keyword/class pairs</li>
                            <li><i class="fas fa-check text-success me-2"></i>Set images per class (1-500)</li>
                            <li><i class="fas fa-check text-success me-2"></i>Each class creates a separate folder</li>
                            <li><i class="fas fa-check text-success me-2"></i>Monitor progress in real-time</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-lightbulb text-warning me-2"></i><strong>Example:</strong></li>
                            <li class="ms-3">Keyword: "cats" → Class: "animals"</li>
                            <li class="ms-3">Keyword: "cars" → Class: "vehicles"</li>
                            <li class="ms-3">Keyword: "flowers" → Class: "nature"</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Progress Display Section (Initially Hidden) -->
    <div class="col-md-10" id="progressSection" style="display: none;">
        <div class="card shadow">
            <div class="card-header bg-info text-white">
                <h4 class="card-title mb-0">
                    <i class="fas fa-cog fa-spin me-2"></i>Bulk Scraping in Progress
                </h4>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <div class="spinner-border text-primary" role="status" id="loadingSpinner">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>

                <div class="progress mb-3" style="height: 25px;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar"
                        style="width: 0%" id="progressBar">
                        <span id="progressText">Initializing...</span>
                    </div>
                </div>

                <div class="alert alert-info" role="alert" id="statusMessage">
                    <i class="fas fa-info-circle me-2"></i>
                    <span id="statusText">Preparing to start bulk scraping...</span>
                </div>

                <!-- Real-time Logs Section -->
                <div class="card mt-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-terminal me-2"></i>Real-time Logs
                        </h6>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-secondary" id="toggleLogs" onclick="toggleLogDisplay()">
                                <i class="fas fa-eye me-1"></i>Show Details
                            </button>
                            <button class="btn btn-outline-primary" id="downloadLogs" onclick="downloadLogs()" disabled>
                                <i class="fas fa-download me-1"></i>Download
                            </button>
                            <button class="btn btn-outline-danger" id="clearLogs" onclick="clearLogs()" disabled>
                                <i class="fas fa-trash me-1"></i>Clear
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0" id="logsContainer" style="display: none;">
                        <div class="log-display" id="logDisplay">
                            <div class="text-muted text-center p-3">
                                <i class="fas fa-clock me-2"></i>Waiting for logs...
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center" id="completionActions" style="display: none;">
                    <div class="mb-3">
                        <i class="fas fa-check-circle fa-3x text-success"></i>
                    </div>
                    <h5 class="text-success">Bulk Scraping Completed!</h5>
                    <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                        <a href="{{ url_for('dashboard') }}" class="btn btn-primary">
                            <i class="fas fa-th-large me-1"></i>View Dashboard
                        </a>
                        <button class="btn btn-outline-primary" onclick="resetToForm()">
                            <i class="fas fa-list me-1"></i>Start New Bulk Search
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-clock me-2"></i>Bulk Processing Status
                </h6>
                <ul class="list-unstyled small">
                    <li><i class="fas fa-check text-success me-2"></i>Processing each keyword/class pair sequentially</li>
                    <li><i class="fas fa-check text-success me-2"></i>Creating separate folders for each class</li>
                    <li><i class="fas fa-check text-success me-2"></i>Downloading and organizing images</li>
                    <li><i class="fas fa-check text-success me-2"></i>Validating and saving image files</li>
                </ul>
                <p class="text-muted small mb-0">
                    <i class="fas fa-info-circle me-1"></i>
                    Bulk processing may take several minutes depending on the number of entries and images per class.
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Include the same log styling from index.html -->
<style>
    .log-display {
        height: 400px;
        overflow-y: auto;
        background-color: #1e1e1e;
        color: #ffffff;
        font-family: 'Courier New', monospace;
        font-size: 0.875rem;
        padding: 1rem;
        border-radius: 0 0 0.375rem 0.375rem;
    }

    .log-entry {
        margin-bottom: 0.25rem;
        padding: 0.25rem 0;
        border-left: 3px solid transparent;
        padding-left: 0.5rem;
    }

    .log-entry.log-info {
        border-left-color: #17a2b8;
        color: #b3d9ff;
    }

    .log-entry.log-success {
        border-left-color: #28a745;
        color: #b3ffb3;
    }

    .log-entry.log-warning {
        border-left-color: #ffc107;
        color: #fff3cd;
    }

    .log-entry.log-error {
        border-left-color: #dc3545;
        color: #ffb3b3;
    }

    .log-entry.log-debug {
        border-left-color: #6c757d;
        color: #d1d1d1;
    }

    .log-timestamp {
        color: #6c757d;
        font-size: 0.8rem;
    }

    .log-level {
        font-weight: bold;
        margin-right: 0.5rem;
    }

    .log-message {
        word-wrap: break-word;
    }

    .log-display::-webkit-scrollbar {
        width: 8px;
    }

    .log-display::-webkit-scrollbar-track {
        background: #2d2d2d;
    }

    .log-display::-webkit-scrollbar-thumb {
        background: #555;
        border-radius: 4px;
    }

    .log-display::-webkit-scrollbar-thumb:hover {
        background: #777;
    }

    .table-row-highlight {
        background-color: #f8f9fa;
    }

    .delete-row-btn {
        opacity: 0.7;
        transition: opacity 0.2s;
    }

    .delete-row-btn:hover {
        opacity: 1;
    }
</style>

<script src="{{ url_for('static', filename='js/bulk_search.js') }}"></script>
{% endblock %}
